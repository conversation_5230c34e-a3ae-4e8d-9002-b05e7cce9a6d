"""
Custom exceptions for PDF to Excel conversion.
"""


class PDFConversionError(Exception):
    """Base exception for PDF conversion errors."""
    pass


class InvalidPDFError(PDFConversionError):
    """Raised when PDF file is invalid or corrupted."""
    pass


class ExtractionError(PDFConversionError):
    """Raised when data extraction from PDF fails."""
    pass


class OutputError(PDFConversionError):
    """Raised when Excel output creation fails."""
    pass


class ValidationError(PDFConversionError):
    """Raised when input validation fails."""
    pass


class DependencyError(PDFConversionError):
    """Raised when required dependencies are missing."""
    pass


class PermissionError(PDFConversionError):
    """Raised when file permission issues occur."""
    pass


class UnsupportedFormatError(PDFConversionError):
    """Raised when PDF format is not supported."""
    pass
