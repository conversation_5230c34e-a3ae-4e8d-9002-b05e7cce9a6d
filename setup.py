"""
Setup script for PDF to Excel Converter
"""

from setuptools import setup, find_packages
import os

# Read README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="pdf-to-excel-converter",
    version="1.0.0",
    author="PDF to Excel Converter",
    author_email="<EMAIL>",
    description="A comprehensive module for converting PDF files to Excel format",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/pdf-to-excel-converter",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Text Processing",
        "Topic :: Utilities",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "advanced": [
            "tabula-py>=2.8.0",
        ],
        "examples": [
            "reportlab>=3.6.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pdf2excel=pdf_to_excel.cli:cli",
        ],
    },
    include_package_data=True,
    package_data={
        "pdf_to_excel": ["*.py"],
    },
    keywords="pdf excel converter table extraction text processing",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/pdf-to-excel-converter/issues",
        "Source": "https://github.com/yourusername/pdf-to-excel-converter",
        "Documentation": "https://github.com/yourusername/pdf-to-excel-converter/blob/main/README.md",
    },
)
