# PDF to Excel Converter Requirements

# PDF processing libraries
PyPDF2==3.0.1
pdfplumber==0.10.0
tabula-py==2.8.2

# Excel processing libraries
openpyxl==3.1.2
pandas==2.1.4
xlsxwriter==3.1.9

# Additional utilities
numpy==1.24.3
python-dateutil==2.8.2
chardet==5.2.0

# CLI and logging
click==8.1.7
colorama==0.4.6
tqdm==4.66.1

# Java runtime for tabula-py (optional, for advanced table extraction)
# Note: tabula-py requires Java to be installed on the system
