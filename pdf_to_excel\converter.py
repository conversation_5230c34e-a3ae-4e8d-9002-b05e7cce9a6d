"""
Main PDF to Excel Converter Module

Contains the main PDFToExcelConverter class that orchestrates the conversion process.
"""

import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import pandas as pd

from .extractors import TextExtractor, TableExtractor
from .writers import ExcelWriter
from .utils import validate_pdf, validate_output_path, setup_logging
from .exceptions import PDFConversionError, InvalidPDFError, ExtractionError, OutputError


class PDFToExcelConverter:
    """Main class for converting PDF files to Excel format."""
    
    def __init__(self, 
                 text_extraction_method: str = "pdfplumber",
                 table_extraction_method: str = "pdfplumber",
                 log_level: str = "INFO"):
        """
        Initialize the PDF to Excel converter.
        
        Args:
            text_extraction_method: Method for text extraction ('pdfplumber' or 'pypdf2')
            table_extraction_method: Method for table extraction ('pdfplumber' or 'tabula')
            log_level: Logging level ('DEBUG', 'INFO', 'WARNING', 'ERROR')
        """
        self.text_extraction_method = text_extraction_method
        self.table_extraction_method = table_extraction_method
        
        # Setup logging
        setup_logging(log_level)
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.text_extractor = TextExtractor()
        self.table_extractor = TableExtractor()
        self.excel_writer = ExcelWriter()
        
        self.logger.info(f"PDFToExcelConverter initialized with text method: {text_extraction_method}, "
                        f"table method: {table_extraction_method}")
    
    def convert_single_pdf(self,
                          pdf_path: str,
                          output_path: str,
                          extract_text: bool = True,
                          extract_tables: bool = True,
                          format_output: bool = True) -> bool:
        """
        Convert a single PDF file to Excel.

        Args:
            pdf_path: Path to the input PDF file
            output_path: Path for the output Excel file
            extract_text: Whether to extract text content
            extract_tables: Whether to extract tables
            format_output: Whether to apply formatting to the Excel file

        Returns:
            True if conversion successful, False otherwise

        Raises:
            InvalidPDFError: If PDF file is invalid or corrupted
            ValidationError: If input parameters are invalid
            ExtractionError: If data extraction fails
            OutputError: If Excel file creation fails
        """
        try:
            # Validate inputs
            self._validate_conversion_inputs(pdf_path, output_path, extract_text, extract_tables)

            self.logger.info(f"Starting conversion of {pdf_path}")

            # Check PDF accessibility and format
            self._check_pdf_accessibility(pdf_path)

            # Extract data with error handling
            extracted_data = self._extract_data_safely(pdf_path, extract_text, extract_tables)

            # Validate extracted data
            if not extracted_data:
                raise ExtractionError("No data could be extracted from the PDF")

            # Write to Excel with error handling
            success = self._write_excel_safely(extracted_data, output_path, format_output)

            if success:
                self.logger.info(f"Conversion completed successfully: {output_path}")
                return True
            else:
                raise OutputError("Failed to write Excel file")

        except (InvalidPDFError, ValidationError, ExtractionError, OutputError) as e:
            self.logger.error(f"Conversion error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during conversion: {e}")
            return False
    
    def convert_multiple_pdfs(self, 
                             pdf_paths: List[str], 
                             output_dir: str,
                             combine_output: bool = False,
                             combined_filename: str = "combined_output.xlsx") -> Dict[str, bool]:
        """
        Convert multiple PDF files to Excel.
        
        Args:
            pdf_paths: List of PDF file paths
            output_dir: Directory for output Excel files
            combine_output: Whether to combine all outputs into one file
            combined_filename: Name for combined output file
        
        Returns:
            Dictionary mapping PDF paths to conversion success status
        """
        results = {}
        output_files = []
        
        # Create output directory if it doesn't exist
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        for pdf_path in pdf_paths:
            try:
                # Generate output filename
                pdf_name = Path(pdf_path).stem
                output_path = os.path.join(output_dir, f"{pdf_name}.xlsx")
                
                # Convert individual PDF
                success = self.convert_single_pdf(pdf_path, output_path)
                results[pdf_path] = success
                
                if success:
                    output_files.append(output_path)
                    
            except Exception as e:
                self.logger.error(f"Error processing {pdf_path}: {e}")
                results[pdf_path] = False
        
        # Combine outputs if requested
        if combine_output and output_files:
            combined_path = os.path.join(output_dir, combined_filename)
            combine_success = self.excel_writer.combine_excel_files(output_files, combined_path)
            if combine_success:
                self.logger.info(f"Combined output saved to: {combined_path}")
        
        return results
    
    def convert_directory(self, 
                         input_dir: str, 
                         output_dir: str,
                         recursive: bool = False,
                         combine_output: bool = False) -> Dict[str, bool]:
        """
        Convert all PDF files in a directory to Excel.
        
        Args:
            input_dir: Directory containing PDF files
            output_dir: Directory for output Excel files
            recursive: Whether to search subdirectories
            combine_output: Whether to combine all outputs
        
        Returns:
            Dictionary mapping PDF paths to conversion success status
        """
        pdf_files = []
        
        if recursive:
            pdf_files = list(Path(input_dir).rglob("*.pdf"))
        else:
            pdf_files = list(Path(input_dir).glob("*.pdf"))
        
        pdf_paths = [str(pdf_file) for pdf_file in pdf_files]
        
        if not pdf_paths:
            self.logger.warning(f"No PDF files found in {input_dir}")
            return {}
        
        self.logger.info(f"Found {len(pdf_paths)} PDF files to convert")
        return self.convert_multiple_pdfs(pdf_paths, output_dir, combine_output)
    
    def get_pdf_info(self, pdf_path: str) -> Dict[str, Any]:
        """
        Get information about a PDF file without full conversion.
        
        Args:
            pdf_path: Path to the PDF file
        
        Returns:
            Dictionary containing PDF information
        """
        info = {
            'file_path': pdf_path,
            'file_size': 0,
            'pages': 0,
            'has_text': False,
            'has_tables': False,
            'text_preview': '',
            'table_count': 0
        }
        
        try:
            if not validate_pdf(pdf_path):
                return info
            
            # File size
            info['file_size'] = os.path.getsize(pdf_path)
            
            # Quick text extraction to check content
            text = self.text_extractor.extract_text(pdf_path, self.text_extraction_method)
            if text:
                info['has_text'] = True
                info['text_preview'] = text[:500] + "..." if len(text) > 500 else text
            
            # Quick table detection
            tables = self.table_extractor.extract_tables(pdf_path, self.table_extraction_method)
            if tables:
                info['has_tables'] = True
                info['table_count'] = len(tables)
            
            # Page count (using pdfplumber)
            import pdfplumber
            with pdfplumber.open(pdf_path) as pdf:
                info['pages'] = len(pdf.pages)
                
        except Exception as e:
            self.logger.error(f"Error getting PDF info: {e}")
        
        return info
    
    def preview_conversion(self, pdf_path: str) -> Dict[str, Any]:
        """
        Preview what would be extracted from a PDF without creating output file.
        
        Args:
            pdf_path: Path to the PDF file
        
        Returns:
            Dictionary containing preview information
        """
        preview = {
            'pdf_info': self.get_pdf_info(pdf_path),
            'text_sample': '',
            'tables_info': [],
            'estimated_output_size': 0
        }
        
        try:
            # Get text sample
            text = self.text_extractor.extract_text(pdf_path, self.text_extraction_method)
            if text:
                preview['text_sample'] = text[:1000] + "..." if len(text) > 1000 else text
            
            # Get table information
            tables = self.table_extractor.extract_tables(pdf_path, self.table_extraction_method)
            for i, table in enumerate(tables):
                table_info = {
                    'name': getattr(table, 'name', f'Table_{i + 1}'),
                    'rows': table.shape[0],
                    'columns': table.shape[1],
                    'sample_data': table.head(3).to_dict() if not table.empty else {}
                }
                preview['tables_info'].append(table_info)
            
            # Estimate output size (rough calculation)
            text_size = len(text) if text else 0
            table_size = sum(table.memory_usage(deep=True).sum() for table in tables) if tables else 0
            preview['estimated_output_size'] = text_size + table_size
            
        except Exception as e:
            self.logger.error(f"Error creating preview: {e}")
        
        return preview

    def _validate_conversion_inputs(self, pdf_path: str, output_path: str,
                                  extract_text: bool, extract_tables: bool) -> None:
        """Validate inputs for conversion."""
        if not extract_text and not extract_tables:
            raise ValidationError("Cannot skip both text and table extraction")

        if not validate_pdf(pdf_path):
            raise InvalidPDFError(f"Invalid PDF file: {pdf_path}")

        if not validate_output_path(output_path):
            raise ValidationError(f"Invalid output path: {output_path}")

    def _check_pdf_accessibility(self, pdf_path: str) -> None:
        """Check if PDF is accessible and readable."""
        try:
            import PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    self.logger.warning(f"PDF is encrypted: {pdf_path}")
                    # Try to decrypt with empty password
                    if not pdf_reader.decrypt(''):
                        raise InvalidPDFError(f"PDF is password protected: {pdf_path}")

                # Check if PDF has pages
                if len(pdf_reader.pages) == 0:
                    raise InvalidPDFError(f"PDF has no pages: {pdf_path}")

        except Exception as e:
            if isinstance(e, InvalidPDFError):
                raise
            raise InvalidPDFError(f"Cannot access PDF file: {e}")

    def _extract_data_safely(self, pdf_path: str, extract_text: bool,
                           extract_tables: bool) -> Dict[str, Any]:
        """Extract data with comprehensive error handling."""
        extracted_data = {}
        extraction_errors = []

        if extract_text:
            try:
                self.logger.info("Extracting text content...")
                text = self.text_extractor.extract_text(pdf_path, self.text_extraction_method)
                if text and text.strip():
                    extracted_data['text'] = text
                    self.logger.info(f"Extracted {len(text)} characters of text")
                else:
                    self.logger.warning("No text content extracted")
            except Exception as e:
                error_msg = f"Text extraction failed: {e}"
                self.logger.error(error_msg)
                extraction_errors.append(error_msg)

        if extract_tables:
            try:
                self.logger.info("Extracting tables...")
                tables = self.table_extractor.extract_tables(pdf_path, self.table_extraction_method)
                if tables:
                    # Filter out empty tables
                    valid_tables = [t for t in tables if not t.empty and t.shape[0] > 0]
                    if valid_tables:
                        extracted_data['tables'] = valid_tables
                        self.logger.info(f"Extracted {len(valid_tables)} valid tables")
                    else:
                        self.logger.warning("No valid tables found")
                else:
                    self.logger.warning("No tables found")
            except Exception as e:
                error_msg = f"Table extraction failed: {e}"
                self.logger.error(error_msg)
                extraction_errors.append(error_msg)

        # If both extractions failed, raise error
        if not extracted_data and extraction_errors:
            raise ExtractionError(f"All extraction methods failed: {'; '.join(extraction_errors)}")

        return extracted_data

    def _write_excel_safely(self, extracted_data: Dict[str, Any],
                          output_path: str, format_output: bool) -> bool:
        """Write Excel file with error handling."""
        try:
            if format_output:
                return self.excel_writer.write_formatted_excel(extracted_data, output_path)
            else:
                # Simple write
                if 'tables' in extracted_data:
                    return self.excel_writer.write_tables_to_excel(extracted_data['tables'], output_path)
                elif 'text' in extracted_data:
                    return self.excel_writer.write_text_to_excel(extracted_data['text'], output_path)
                else:
                    return False
        except Exception as e:
            raise OutputError(f"Failed to create Excel file: {e}")
