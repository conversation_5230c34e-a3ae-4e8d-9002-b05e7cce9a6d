"""
PDF Data Extraction Module

Contains classes for extracting text and tabular data from PDF files.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import pdfplumber
import PyPDF2
from io import BytesIO

try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False
    logging.warning("tabula-py not available. Advanced table extraction will be limited.")


class TextExtractor:
    """Extracts text content from PDF files."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_with_pypdf2(self, pdf_path: str) -> str:
        """Extract text using PyPDF2."""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                # Handle encrypted PDFs
                if pdf_reader.is_encrypted:
                    if not pdf_reader.decrypt(''):
                        self.logger.error("PDF is password protected")
                        return ""

                text = ""
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as e:
                        self.logger.warning(f"Failed to extract text from page {page_num + 1}: {e}")
                        continue

                return text.strip()
        except FileNotFoundError:
            self.logger.error(f"PDF file not found: {pdf_path}")
            return ""
        except Exception as e:
            self.logger.error(f"Error extracting text with PyPDF2: {e}")
            return ""
    
    def extract_with_pdfplumber(self, pdf_path: str) -> str:
        """Extract text using pdfplumber (more accurate)."""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as e:
                        self.logger.warning(f"Failed to extract text from page {page_num + 1}: {e}")
                        continue
            return text.strip()
        except FileNotFoundError:
            self.logger.error(f"PDF file not found: {pdf_path}")
            return ""
        except Exception as e:
            self.logger.error(f"Error extracting text with pdfplumber: {e}")
            return ""
    
    def extract_text(self, pdf_path: str, method: str = "pdfplumber") -> str:
        """
        Extract text from PDF using specified method.
        
        Args:
            pdf_path: Path to the PDF file
            method: Extraction method ('pdfplumber' or 'pypdf2')
        
        Returns:
            Extracted text as string
        """
        if method == "pdfplumber":
            return self.extract_with_pdfplumber(pdf_path)
        elif method == "pypdf2":
            return self.extract_with_pypdf2(pdf_path)
        else:
            raise ValueError(f"Unknown extraction method: {method}")


class TableExtractor:
    """Extracts tabular data from PDF files."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_tables_pdfplumber(self, pdf_path: str) -> List[pd.DataFrame]:
        """Extract tables using pdfplumber."""
        tables = []
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_tables = page.extract_tables()
                    for table_num, table in enumerate(page_tables):
                        if table and len(table) > 1:  # Ensure table has data
                            df = pd.DataFrame(table[1:], columns=table[0])
                            df.name = f"Page_{page_num + 1}_Table_{table_num + 1}"
                            tables.append(df)
        except Exception as e:
            self.logger.error(f"Error extracting tables with pdfplumber: {e}")
        
        return tables
    
    def extract_tables_tabula(self, pdf_path: str) -> List[pd.DataFrame]:
        """Extract tables using tabula-py (requires Java)."""
        if not TABULA_AVAILABLE:
            self.logger.warning("tabula-py not available. Using pdfplumber instead.")
            return self.extract_tables_pdfplumber(pdf_path)
        
        tables = []
        try:
            # Extract all tables from all pages
            dfs = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)
            for i, df in enumerate(dfs):
                if not df.empty:
                    df.name = f"Table_{i + 1}"
                    tables.append(df)
        except Exception as e:
            self.logger.error(f"Error extracting tables with tabula: {e}")
            # Fallback to pdfplumber
            return self.extract_tables_pdfplumber(pdf_path)
        
        return tables
    
    def extract_tables(self, pdf_path: str, method: str = "pdfplumber") -> List[pd.DataFrame]:
        """
        Extract tables from PDF using specified method.
        
        Args:
            pdf_path: Path to the PDF file
            method: Extraction method ('pdfplumber' or 'tabula')
        
        Returns:
            List of pandas DataFrames containing extracted tables
        """
        if method == "pdfplumber":
            return self.extract_tables_pdfplumber(pdf_path)
        elif method == "tabula":
            return self.extract_tables_tabula(pdf_path)
        else:
            raise ValueError(f"Unknown extraction method: {method}")
    
    def detect_table_structure(self, text: str) -> List[Dict[str, Any]]:
        """
        Detect potential table structures in text.
        
        Args:
            text: Raw text from PDF
        
        Returns:
            List of dictionaries containing detected table information
        """
        tables = []
        lines = text.split('\n')
        
        # Simple heuristic: look for lines with multiple whitespace-separated values
        potential_table_lines = []
        for i, line in enumerate(lines):
            # Skip empty lines
            if not line.strip():
                continue
            
            # Look for lines with multiple columns (3+ whitespace-separated items)
            parts = line.split()
            if len(parts) >= 3:
                potential_table_lines.append((i, line, parts))
        
        # Group consecutive table lines
        if potential_table_lines:
            current_table = []
            for i, (line_num, line, parts) in enumerate(potential_table_lines):
                if i == 0 or line_num - potential_table_lines[i-1][0] <= 2:
                    current_table.append((line_num, line, parts))
                else:
                    if len(current_table) >= 2:  # At least header + 1 data row
                        tables.append({
                            'start_line': current_table[0][0],
                            'end_line': current_table[-1][0],
                            'rows': len(current_table),
                            'columns': len(current_table[0][2]),
                            'data': current_table
                        })
                    current_table = [(line_num, line, parts)]
            
            # Don't forget the last table
            if len(current_table) >= 2:
                tables.append({
                    'start_line': current_table[0][0],
                    'end_line': current_table[-1][0],
                    'rows': len(current_table),
                    'columns': len(current_table[0][2]),
                    'data': current_table
                })
        
        return tables
