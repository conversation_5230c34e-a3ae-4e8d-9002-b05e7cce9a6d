"""
Command Line Interface for PDF to Excel Converter
"""

import click
import os
import sys
from pathlib import Path
from typing import List
import json

from .converter import PDFToExcelConverter
from .utils import validate_pdf, format_file_size, get_file_size_mb


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """PDF to Excel Converter - Convert PDF files to Excel format with table and text extraction."""
    pass


@cli.command()
@click.argument('pdf_path', type=click.Path(exists=True))
@click.argument('output_path', type=click.Path())
@click.option('--text-method', default='pdfplumber', 
              type=click.Choice(['pdfplumber', 'pypdf2']),
              help='Method for text extraction')
@click.option('--table-method', default='pdfplumber',
              type=click.Choice(['pdfplumber', 'tabula']),
              help='Method for table extraction')
@click.option('--no-text', is_flag=True, help='Skip text extraction')
@click.option('--no-tables', is_flag=True, help='Skip table extraction')
@click.option('--no-format', is_flag=True, help='Skip Excel formatting')
@click.option('--log-level', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              help='Logging level')
def convert(pdf_path, output_path, text_method, table_method, no_text, no_tables, no_format, log_level):
    """Convert a single PDF file to Excel format."""
    
    if no_text and no_tables:
        click.echo("Error: Cannot skip both text and table extraction", err=True)
        sys.exit(1)
    
    # Initialize converter
    converter = PDFToExcelConverter(
        text_extraction_method=text_method,
        table_extraction_method=table_method,
        log_level=log_level
    )
    
    # Show file info
    file_size = format_file_size(os.path.getsize(pdf_path))
    click.echo(f"Converting: {pdf_path} ({file_size})")
    click.echo(f"Output: {output_path}")
    
    # Perform conversion
    with click.progressbar(length=100, label='Converting PDF') as bar:
        success = converter.convert_single_pdf(
            pdf_path=pdf_path,
            output_path=output_path,
            extract_text=not no_text,
            extract_tables=not no_tables,
            format_output=not no_format
        )
        bar.update(100)
    
    if success:
        output_size = format_file_size(os.path.getsize(output_path))
        click.echo(f"✅ Conversion successful! Output size: {output_size}")
    else:
        click.echo("❌ Conversion failed!", err=True)
        sys.exit(1)


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True, file_okay=False))
@click.argument('output_dir', type=click.Path())
@click.option('--recursive', '-r', is_flag=True, help='Search subdirectories recursively')
@click.option('--combine', is_flag=True, help='Combine all outputs into one Excel file')
@click.option('--combined-name', default='combined_output.xlsx', help='Name for combined output file')
@click.option('--text-method', default='pdfplumber',
              type=click.Choice(['pdfplumber', 'pypdf2']),
              help='Method for text extraction')
@click.option('--table-method', default='pdfplumber',
              type=click.Choice(['pdfplumber', 'tabula']),
              help='Method for table extraction')
@click.option('--log-level', default='INFO',
              type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              help='Logging level')
def batch(input_dir, output_dir, recursive, combine, combined_name, text_method, table_method, log_level):
    """Convert all PDF files in a directory to Excel format."""
    
    # Initialize converter
    converter = PDFToExcelConverter(
        text_extraction_method=text_method,
        table_extraction_method=table_method,
        log_level=log_level
    )
    
    # Find PDF files
    if recursive:
        pdf_files = list(Path(input_dir).rglob("*.pdf"))
    else:
        pdf_files = list(Path(input_dir).glob("*.pdf"))
    
    if not pdf_files:
        click.echo(f"No PDF files found in {input_dir}")
        return
    
    click.echo(f"Found {len(pdf_files)} PDF files to convert")
    
    # Convert files
    results = converter.convert_multiple_pdfs(
        pdf_paths=[str(f) for f in pdf_files],
        output_dir=output_dir,
        combine_output=combine,
        combined_filename=combined_name
    )
    
    # Show results
    successful = sum(1 for success in results.values() if success)
    failed = len(results) - successful
    
    click.echo(f"\n📊 Conversion Results:")
    click.echo(f"✅ Successful: {successful}")
    click.echo(f"❌ Failed: {failed}")
    
    if failed > 0:
        click.echo("\nFailed files:")
        for pdf_path, success in results.items():
            if not success:
                click.echo(f"  - {pdf_path}")


@cli.command()
@click.argument('pdf_path', type=click.Path(exists=True))
@click.option('--detailed', is_flag=True, help='Show detailed information')
def info(pdf_path, detailed):
    """Get information about a PDF file."""
    
    if not validate_pdf(pdf_path):
        click.echo("Error: Invalid PDF file", err=True)
        sys.exit(1)
    
    converter = PDFToExcelConverter()
    
    if detailed:
        info_data = converter.get_pdf_info(pdf_path)
        
        click.echo(f"📄 PDF Information: {pdf_path}")
        click.echo(f"File size: {format_file_size(info_data['file_size'])}")
        click.echo(f"Pages: {info_data['pages']}")
        click.echo(f"Has text: {'Yes' if info_data['has_text'] else 'No'}")
        click.echo(f"Has tables: {'Yes' if info_data['has_tables'] else 'No'}")
        click.echo(f"Table count: {info_data['table_count']}")
        
        if info_data['text_preview']:
            click.echo(f"\nText preview:")
            click.echo(f"{info_data['text_preview']}")
    else:
        file_size = format_file_size(os.path.getsize(pdf_path))
        click.echo(f"📄 {pdf_path} ({file_size})")


@cli.command()
@click.argument('pdf_path', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='Save preview to JSON file')
def preview(pdf_path, output):
    """Preview what would be extracted from a PDF file."""
    
    if not validate_pdf(pdf_path):
        click.echo("Error: Invalid PDF file", err=True)
        sys.exit(1)
    
    converter = PDFToExcelConverter()
    
    click.echo("🔍 Generating preview...")
    preview_data = converter.preview_conversion(pdf_path)
    
    # Display preview
    click.echo(f"\n📄 Preview for: {pdf_path}")
    click.echo(f"File size: {format_file_size(preview_data['pdf_info']['file_size'])}")
    click.echo(f"Pages: {preview_data['pdf_info']['pages']}")
    click.echo(f"Estimated output size: {format_file_size(preview_data['estimated_output_size'])}")
    
    if preview_data['text_sample']:
        click.echo(f"\n📝 Text sample:")
        click.echo(preview_data['text_sample'])
    
    if preview_data['tables_info']:
        click.echo(f"\n📊 Tables found: {len(preview_data['tables_info'])}")
        for i, table_info in enumerate(preview_data['tables_info']):
            click.echo(f"  Table {i+1}: {table_info['rows']} rows × {table_info['columns']} columns")
    
    # Save to file if requested
    if output:
        with open(output, 'w') as f:
            json.dump(preview_data, f, indent=2, default=str)
        click.echo(f"\n💾 Preview saved to: {output}")


@cli.command()
@click.option('--check-java', is_flag=True, help='Check if Java is available for tabula-py')
def check():
    """Check system requirements and dependencies."""
    
    click.echo("🔧 Checking PDF to Excel Converter requirements...")
    
    # Check Python packages
    required_packages = [
        'PyPDF2', 'pdfplumber', 'openpyxl', 'pandas', 'click'
    ]
    
    optional_packages = ['tabula']
    
    click.echo("\n📦 Required packages:")
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            click.echo(f"  ✅ {package}")
        except ImportError:
            click.echo(f"  ❌ {package} - Not installed")
    
    click.echo("\n📦 Optional packages:")
    for package in optional_packages:
        try:
            __import__(package)
            click.echo(f"  ✅ {package}")
        except ImportError:
            click.echo(f"  ⚠️  {package} - Not installed (advanced table extraction unavailable)")
    
    # Check Java for tabula-py
    if check_java:
        click.echo("\n☕ Java availability (for tabula-py):")
        import subprocess
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                click.echo("  ✅ Java is available")
            else:
                click.echo("  ❌ Java not found")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            click.echo("  ❌ Java not found or not accessible")


if __name__ == '__main__':
    cli()
