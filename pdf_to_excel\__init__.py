"""
PDF to Excel Converter Module

A comprehensive module for converting PDF files to Excel format.
Supports various PDF types including text-based PDFs and PDFs with tables.
"""

from .converter import PDFToExcelConverter
from .extractors import TextExtractor, TableExtractor
from .writers import ExcelWriter
from .utils import validate_pdf, validate_output_path

__version__ = "1.0.0"
__author__ = "PDF to Excel Converter"
__email__ = "<EMAIL>"

__all__ = [
    "PDFToExcelConverter",
    "TextExtractor", 
    "TableExtractor",
    "ExcelWriter",
    "validate_pdf",
    "validate_output_path"
]
