# PDF to Excel Converter

A comprehensive Python module for converting PDF files to Excel format with advanced text and table extraction capabilities.

## Features

- **Multiple Extraction Methods**: Support for PyPDF2, pdfplumber, and tabula-py
- **Text Extraction**: Extract and preserve text content from PDFs
- **Table Detection**: Automatically detect and extract tables from PDFs
- **Excel Formatting**: Create well-formatted Excel files with multiple sheets
- **Batch Processing**: Convert multiple PDFs at once
- **CLI Interface**: Easy-to-use command-line interface
- **Preview Mode**: Preview extraction results before conversion
- **Flexible Output**: Combine multiple PDFs into single Excel file or separate files

## Installation

1. Clone or download this repository
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

### Optional Dependencies

For advanced table extraction with tabula-py, you'll need Java installed:

- **Windows**: Download and install Java from [Oracle](https://www.oracle.com/java/technologies/downloads/)
- **macOS**: `brew install openjdk`
- **Linux**: `sudo apt-get install default-jdk` (Ubuntu/Debian)

## Quick Start

### Using the Command Line Interface

```bash
# Convert a single PDF
python main.py convert input.pdf output.xlsx

# Convert all PDFs in a directory
python main.py batch pdf_folder excel_output

# Get PDF information
python main.py info document.pdf

# Preview extraction results
python main.py preview document.pdf

# Check system requirements
python main.py check --check-java
```

### Using the Python API

```python
from pdf_to_excel import PDFToExcelConverter

# Initialize converter
converter = PDFToExcelConverter()

# Convert single PDF
success = converter.convert_single_pdf(
    pdf_path="document.pdf",
    output_path="output.xlsx"
)

# Convert multiple PDFs
results = converter.convert_multiple_pdfs(
    pdf_paths=["doc1.pdf", "doc2.pdf"],
    output_dir="excel_files"
)
```

## Usage Examples

### Basic Conversion

```python
from pdf_to_excel import PDFToExcelConverter

converter = PDFToExcelConverter()

# Simple conversion
converter.convert_single_pdf("input.pdf", "output.xlsx")
```

### Advanced Configuration

```python
converter = PDFToExcelConverter(
    text_extraction_method="pdfplumber",  # or "pypdf2"
    table_extraction_method="tabula",     # or "pdfplumber"
    log_level="INFO"
)

# Convert with custom options
converter.convert_single_pdf(
    pdf_path="document.pdf",
    output_path="formatted_output.xlsx",
    extract_text=True,
    extract_tables=True,
    format_output=True
)
```

### Batch Processing

```python
# Convert entire directory
results = converter.convert_directory(
    input_dir="pdf_files",
    output_dir="excel_files",
    recursive=True,
    combine_output=True
)

# Check results
successful = sum(1 for success in results.values() if success)
print(f"Converted {successful}/{len(results)} files successfully")
```

### PDF Information and Preview

```python
# Get PDF information
info = converter.get_pdf_info("document.pdf")
print(f"Pages: {info['pages']}, Has tables: {info['has_tables']}")

# Preview extraction
preview = converter.preview_conversion("document.pdf")
print(f"Tables found: {len(preview['tables_info'])}")
```

## Command Line Interface

The CLI provides several commands for different use cases:

### Convert Command

```bash
python main.py convert [OPTIONS] PDF_PATH OUTPUT_PATH

Options:
  --text-method [pdfplumber|pypdf2]   Method for text extraction
  --table-method [pdfplumber|tabula]  Method for table extraction
  --no-text                          Skip text extraction
  --no-tables                        Skip table extraction
  --no-format                        Skip Excel formatting
  --log-level [DEBUG|INFO|WARNING|ERROR]
```

### Batch Command

```bash
python main.py batch [OPTIONS] INPUT_DIR OUTPUT_DIR

Options:
  -r, --recursive                     Search subdirectories
  --combine                          Combine all outputs into one file
  --combined-name TEXT               Name for combined output file
  --text-method [pdfplumber|pypdf2]
  --table-method [pdfplumber|tabula]
```

### Info Command

```bash
python main.py info [OPTIONS] PDF_PATH

Options:
  --detailed    Show detailed information
```

### Preview Command

```bash
python main.py preview [OPTIONS] PDF_PATH

Options:
  -o, --output PATH    Save preview to JSON file
```

## Extraction Methods

### Text Extraction

1. **pdfplumber** (Recommended): More accurate text extraction, better handling of complex layouts
2. **PyPDF2**: Faster but may miss some text in complex PDFs

### Table Extraction

1. **pdfplumber**: Good for simple tables, works without Java
2. **tabula-py**: Advanced table extraction, requires Java, better for complex tables

## Output Formats

The converter creates Excel files with multiple sheets:

- **Summary**: Overview of conversion results
- **Text_Content**: Extracted text content (if enabled)
- **Table_1, Table_2, etc.**: Individual tables found in the PDF

## Error Handling

The module includes comprehensive error handling:

- Invalid PDF files are detected and skipped
- Extraction failures are logged with detailed error messages
- Partial conversions are supported (e.g., text-only if no tables found)
- Output path validation ensures files can be written

## Performance Considerations

- Large PDFs may take several minutes to process
- Table extraction with tabula-py is slower but more accurate
- Memory usage scales with PDF size and number of tables
- Use preview mode to estimate processing time

## Troubleshooting

### Common Issues

1. **"Java not found" error**: Install Java for tabula-py support
2. **Empty Excel output**: PDF may be image-based (requires OCR)
3. **Table extraction fails**: Try different extraction methods
4. **Permission errors**: Check write permissions for output directory

### Debug Mode

Enable debug logging for detailed information:

```python
converter = PDFToExcelConverter(log_level="DEBUG")
```

Or via CLI:
```bash
python main.py convert --log-level DEBUG input.pdf output.xlsx
```

## Dependencies

### Required
- PyPDF2: PDF reading and basic text extraction
- pdfplumber: Advanced PDF analysis and table detection
- openpyxl: Excel file creation and formatting
- pandas: Data manipulation and analysis
- click: Command-line interface

### Optional
- tabula-py: Advanced table extraction (requires Java)
- reportlab: For creating sample PDFs (examples only)

## License

This project is open source. Feel free to use, modify, and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## Support

For questions or issues:
1. Check the troubleshooting section
2. Run the system check: `python main.py check --check-java`
3. Enable debug logging for detailed error information
