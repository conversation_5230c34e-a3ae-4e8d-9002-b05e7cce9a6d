"""
Simple test script for PDF to Excel Converter
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pdf_to_excel import PDFToExcelConverter
from pdf_to_excel.utils import validate_pdf, get_file_size_mb


def test_module_import():
    """Test if all modules can be imported successfully."""
    print("Testing module imports...")
    
    try:
        from pdf_to_excel import PDFToExcelConverter, TextExtractor, TableExtractor, ExcelWriter
        from pdf_to_excel.utils import validate_pdf, validate_output_path
        from pdf_to_excel.exceptions import PDFConversionError
        print("✅ All modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_converter_initialization():
    """Test converter initialization with different parameters."""
    print("\nTesting converter initialization...")
    
    try:
        # Test default initialization
        converter1 = PDFToExcelConverter()
        print("✅ Default initialization successful")
        
        # Test custom initialization
        converter2 = PDFToExcelConverter(
            text_extraction_method="pypdf2",
            table_extraction_method="pdfplumber",
            log_level="DEBUG"
        )
        print("✅ Custom initialization successful")
        
        return True
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        return False


def test_validation_functions():
    """Test validation functions."""
    print("\nTesting validation functions...")
    
    try:
        # Test PDF validation with non-existent file
        result1 = validate_pdf("nonexistent.pdf")
        assert result1 == False, "Should return False for non-existent file"
        print("✅ PDF validation for non-existent file works")
        
        # Test output path validation
        from pdf_to_excel.utils import validate_output_path
        result2 = validate_output_path("test_output.xlsx")
        print(f"✅ Output path validation works: {result2}")
        
        return True
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False


def test_pdf_info_without_file():
    """Test PDF info function without actual PDF file."""
    print("\nTesting PDF info function...")
    
    try:
        converter = PDFToExcelConverter()
        
        # This should handle the missing file gracefully
        info = converter.get_pdf_info("nonexistent.pdf")
        
        expected_keys = ['file_path', 'file_size', 'pages', 'has_text', 'has_tables', 'text_preview', 'table_count']
        for key in expected_keys:
            assert key in info, f"Missing key: {key}"
        
        print("✅ PDF info function handles missing file gracefully")
        return True
    except Exception as e:
        print(f"❌ PDF info error: {e}")
        return False


def create_sample_pdf_simple():
    """Create a simple sample PDF for testing if reportlab is available."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        print("\nCreating sample PDF for testing...")
        
        pdf_path = "test_sample.pdf"
        c = canvas.Canvas(pdf_path, pagesize=letter)
        
        # Add simple content
        c.setFont("Helvetica", 12)
        c.drawString(100, 750, "Test PDF for Conversion")
        c.drawString(100, 730, "This is a simple test document.")
        
        # Add table-like data
        c.drawString(100, 700, "Name    Age    City")
        c.drawString(100, 680, "John    25     NYC")
        c.drawString(100, 660, "Jane    30     LA")
        
        c.save()
        print(f"✅ Sample PDF created: {pdf_path}")
        return pdf_path
    except ImportError:
        print("⚠️  reportlab not available, skipping PDF creation")
        return None
    except Exception as e:
        print(f"❌ Error creating sample PDF: {e}")
        return None


def test_with_sample_pdf():
    """Test conversion with a sample PDF if available."""
    print("\nTesting with sample PDF...")
    
    # Try to create sample PDF
    pdf_path = create_sample_pdf_simple()
    
    if not pdf_path or not os.path.exists(pdf_path):
        print("⚠️  No sample PDF available, skipping conversion test")
        return True
    
    try:
        converter = PDFToExcelConverter()
        
        # Test PDF info
        info = converter.get_pdf_info(pdf_path)
        print(f"✅ PDF info retrieved: {info['pages']} pages, {info['file_size']} bytes")
        
        # Test preview
        preview = converter.preview_conversion(pdf_path)
        print(f"✅ Preview generated: {len(preview['text_sample'])} chars preview")
        
        # Test actual conversion
        output_path = "test_output.xlsx"
        success = converter.convert_single_pdf(pdf_path, output_path)
        
        if success and os.path.exists(output_path):
            print(f"✅ Conversion successful: {output_path}")
            
            # Clean up
            os.remove(pdf_path)
            os.remove(output_path)
            print("✅ Test files cleaned up")
        else:
            print("❌ Conversion failed")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Conversion test error: {e}")
        return False


def run_all_tests():
    """Run all tests."""
    print("=" * 60)
    print("PDF to Excel Converter - Test Suite")
    print("=" * 60)
    
    tests = [
        test_module_import,
        test_converter_initialization,
        test_validation_functions,
        test_pdf_info_without_file,
        test_with_sample_pdf,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
