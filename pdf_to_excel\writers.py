"""
Excel Writing Module

Contains classes for writing extracted data to Excel files.
"""

import logging
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows


class ExcelWriter:
    """Writes extracted data to Excel files with formatting."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def write_text_to_excel(self, text: str, output_path: str, sheet_name: str = "Text_Content") -> bool:
        """
        Write plain text to Excel file.
        
        Args:
            text: Text content to write
            output_path: Path for output Excel file
            sheet_name: Name of the Excel sheet
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Split text into lines and create DataFrame
            lines = text.split('\n')
            df = pd.DataFrame({'Content': lines})
            
            # Write to Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.logger.info(f"Text successfully written to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error writing text to Excel: {e}")
            return False
    
    def write_tables_to_excel(self, tables: List[pd.DataFrame], output_path: str) -> bool:
        """
        Write multiple tables to Excel file with separate sheets.
        
        Args:
            tables: List of pandas DataFrames
            output_path: Path for output Excel file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for i, table in enumerate(tables):
                    sheet_name = getattr(table, 'name', f'Table_{i + 1}')
                    # Ensure sheet name is valid (max 31 chars, no special chars)
                    sheet_name = self._sanitize_sheet_name(sheet_name)
                    table.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.logger.info(f"Tables successfully written to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error writing tables to Excel: {e}")
            return False
    
    def write_formatted_excel(self, data: Dict[str, Any], output_path: str) -> bool:
        """
        Write data to Excel with advanced formatting.
        
        Args:
            data: Dictionary containing text and tables
            output_path: Path for output Excel file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            workbook = openpyxl.Workbook()
            
            # Remove default sheet
            workbook.remove(workbook.active)
            
            # Add text content if available
            if 'text' in data and data['text']:
                self._add_text_sheet(workbook, data['text'])
            
            # Add table sheets if available
            if 'tables' in data and data['tables']:
                self._add_table_sheets(workbook, data['tables'])
            
            # Add summary sheet
            self._add_summary_sheet(workbook, data)
            
            # Save workbook
            workbook.save(output_path)
            self.logger.info(f"Formatted Excel file saved to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating formatted Excel: {e}")
            return False
    
    def _add_text_sheet(self, workbook: openpyxl.Workbook, text: str) -> None:
        """Add formatted text sheet to workbook."""
        sheet = workbook.create_sheet("Text_Content")
        
        # Split text into paragraphs
        paragraphs = text.split('\n\n')
        
        row = 1
        for paragraph in paragraphs:
            if paragraph.strip():
                lines = paragraph.split('\n')
                for line in lines:
                    if line.strip():
                        cell = sheet.cell(row=row, column=1, value=line.strip())
                        cell.alignment = Alignment(wrap_text=True)
                        row += 1
                row += 1  # Add space between paragraphs
        
        # Adjust column width
        sheet.column_dimensions['A'].width = 100
    
    def _add_table_sheets(self, workbook: openpyxl.Workbook, tables: List[pd.DataFrame]) -> None:
        """Add formatted table sheets to workbook."""
        for i, table in enumerate(tables):
            sheet_name = getattr(table, 'name', f'Table_{i + 1}')
            sheet_name = self._sanitize_sheet_name(sheet_name)
            sheet = workbook.create_sheet(sheet_name)
            
            # Add data to sheet
            for r in dataframe_to_rows(table, index=False, header=True):
                sheet.append(r)
            
            # Format header row
            if sheet.max_row > 0:
                for cell in sheet[1]:
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")
            
            # Auto-adjust column widths
            for column in sheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                sheet.column_dimensions[column_letter].width = adjusted_width
    
    def _add_summary_sheet(self, workbook: openpyxl.Workbook, data: Dict[str, Any]) -> None:
        """Add summary sheet with conversion information."""
        sheet = workbook.create_sheet("Summary", 0)  # Insert at beginning
        
        # Title
        sheet['A1'] = "PDF to Excel Conversion Summary"
        sheet['A1'].font = Font(size=16, bold=True)
        sheet['A1'].fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        sheet['A1'].font = Font(size=16, bold=True, color="FFFFFF")
        
        row = 3
        
        # Text information
        if 'text' in data and data['text']:
            sheet[f'A{row}'] = "Text Content:"
            sheet[f'A{row}'].font = Font(bold=True)
            row += 1
            
            text_lines = len(data['text'].split('\n'))
            text_chars = len(data['text'])
            sheet[f'B{row}'] = f"Lines extracted: {text_lines}"
            row += 1
            sheet[f'B{row}'] = f"Characters extracted: {text_chars}"
            row += 2
        
        # Table information
        if 'tables' in data and data['tables']:
            sheet[f'A{row}'] = "Tables Found:"
            sheet[f'A{row}'].font = Font(bold=True)
            row += 1
            
            for i, table in enumerate(data['tables']):
                table_name = getattr(table, 'name', f'Table_{i + 1}')
                sheet[f'B{row}'] = f"{table_name}: {table.shape[0]} rows × {table.shape[1]} columns"
                row += 1
        
        # Auto-adjust column widths
        sheet.column_dimensions['A'].width = 25
        sheet.column_dimensions['B'].width = 40
    
    def _sanitize_sheet_name(self, name: str) -> str:
        """Sanitize sheet name for Excel compatibility."""
        # Remove invalid characters
        invalid_chars = ['\\', '/', '*', '[', ']', ':', '?']
        for char in invalid_chars:
            name = name.replace(char, '_')
        
        # Limit length to 31 characters
        if len(name) > 31:
            name = name[:31]
        
        return name
    
    def combine_excel_files(self, file_paths: List[str], output_path: str) -> bool:
        """
        Combine multiple Excel files into one.
        
        Args:
            file_paths: List of Excel file paths to combine
            output_path: Path for combined Excel file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            combined_workbook = openpyxl.Workbook()
            combined_workbook.remove(combined_workbook.active)
            
            for file_path in file_paths:
                source_workbook = openpyxl.load_workbook(file_path)
                file_name = Path(file_path).stem
                
                for sheet_name in source_workbook.sheetnames:
                    source_sheet = source_workbook[sheet_name]
                    new_sheet_name = f"{file_name}_{sheet_name}"
                    new_sheet_name = self._sanitize_sheet_name(new_sheet_name)
                    
                    target_sheet = combined_workbook.create_sheet(new_sheet_name)
                    
                    # Copy data
                    for row in source_sheet.iter_rows():
                        for cell in row:
                            target_sheet[cell.coordinate].value = cell.value
                            if cell.has_style:
                                target_sheet[cell.coordinate].font = cell.font
                                target_sheet[cell.coordinate].fill = cell.fill
                                target_sheet[cell.coordinate].alignment = cell.alignment
            
            combined_workbook.save(output_path)
            self.logger.info(f"Combined Excel file saved to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error combining Excel files: {e}")
            return False
